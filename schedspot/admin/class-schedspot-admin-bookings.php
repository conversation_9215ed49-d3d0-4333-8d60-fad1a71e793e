<?php
/**
 * Admin Bookings Management Class
 *
 * @package SchedSpot
 * @version 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * SchedSpot_Admin_Bookings Class.
 *
 * Handles booking management in the admin area including
 * listing, editing, status updates, and booking details.
 *
 * @class SchedSpot_Admin_Bookings
 * @version 1.0.0
 */
class SchedSpot_Admin_Bookings {

    /**
     * Constructor.
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->init();
    }

    /**
     * Initialize booking management functionality.
     *
     * @since 1.0.0
     */
    public function init() {
        add_action( 'wp_ajax_schedspot_update_booking_status', array( $this, 'handle_status_update' ) );
        add_action( 'wp_ajax_schedspot_delete_booking', array( $this, 'handle_booking_deletion' ) );
        add_action( 'wp_ajax_schedspot_refresh_bookings', array( $this, 'handle_refresh_bookings' ) );
    }

    /**
     * Bookings page callback.
     *
     * @since 1.0.0
     */
    public static function bookings_page() {
        $instance = new self();
        
        // Handle bulk actions
        if ( isset( $_POST['action'] ) && $_POST['action'] !== '-1' ) {
            $instance->handle_bulk_action();
        }

        // Handle individual booking actions
        if ( isset( $_GET['action'] ) && isset( $_GET['booking_id'] ) ) {
            $instance->handle_individual_action();
        }

        // Get bookings with filters
        $bookings = $instance->get_filtered_bookings();
        $total_bookings = $instance->get_bookings_count();
        
        // Load template
        include SCHEDSPOT_PLUGIN_DIR . 'templates/admin/bookings-list.php';
    }

    /**
     * Get filtered bookings.
     *
     * @since 1.0.0
     * @return array Filtered bookings.
     */
    private function get_filtered_bookings() {
        global $wpdb;
        
        $per_page = 20;
        $current_page = isset( $_GET['paged'] ) ? max( 1, intval( $_GET['paged'] ) ) : 1;
        $offset = ( $current_page - 1 ) * $per_page;
        
        $where_clauses = array( '1=1' );
        $where_values = array();
        
        // Status filter
        if ( isset( $_GET['status'] ) && $_GET['status'] !== 'all' ) {
            $where_clauses[] = 'status = %s';
            $where_values[] = sanitize_text_field( $_GET['status'] );
        }
        
        // Date range filter
        if ( isset( $_GET['date_from'] ) && $_GET['date_from'] ) {
            $where_clauses[] = 'booking_date >= %s';
            $where_values[] = sanitize_text_field( $_GET['date_from'] );
        }
        
        if ( isset( $_GET['date_to'] ) && $_GET['date_to'] ) {
            $where_clauses[] = 'booking_date <= %s';
            $where_values[] = sanitize_text_field( $_GET['date_to'] );
        }
        
        // Search filter
        if ( isset( $_GET['s'] ) && $_GET['s'] ) {
            $search = '%' . $wpdb->esc_like( sanitize_text_field( $_GET['s'] ) ) . '%';
            $where_clauses[] = '(client_name LIKE %s OR client_email LIKE %s OR notes LIKE %s)';
            $where_values[] = $search;
            $where_values[] = $search;
            $where_values[] = $search;
        }
        
        $where_clause = implode( ' AND ', $where_clauses );
        
        $query = "SELECT * FROM {$wpdb->prefix}schedspot_bookings WHERE {$where_clause} ORDER BY created_at DESC LIMIT %d OFFSET %d";
        $where_values[] = $per_page;
        $where_values[] = $offset;
        
        if ( ! empty( $where_values ) ) {
            $query = $wpdb->prepare( $query, $where_values );
        }
        
        return $wpdb->get_results( $query );
    }

    /**
     * Get total bookings count.
     *
     * @since 1.0.0
     * @return int Total bookings count.
     */
    private function get_bookings_count() {
        global $wpdb;
        
        $where_clauses = array( '1=1' );
        $where_values = array();
        
        // Apply same filters as get_filtered_bookings
        if ( isset( $_GET['status'] ) && $_GET['status'] !== 'all' ) {
            $where_clauses[] = 'status = %s';
            $where_values[] = sanitize_text_field( $_GET['status'] );
        }
        
        if ( isset( $_GET['date_from'] ) && $_GET['date_from'] ) {
            $where_clauses[] = 'booking_date >= %s';
            $where_values[] = sanitize_text_field( $_GET['date_from'] );
        }
        
        if ( isset( $_GET['date_to'] ) && $_GET['date_to'] ) {
            $where_clauses[] = 'booking_date <= %s';
            $where_values[] = sanitize_text_field( $_GET['date_to'] );
        }
        
        if ( isset( $_GET['s'] ) && $_GET['s'] ) {
            $search = '%' . $wpdb->esc_like( sanitize_text_field( $_GET['s'] ) ) . '%';
            $where_clauses[] = '(client_name LIKE %s OR client_email LIKE %s OR notes LIKE %s)';
            $where_values[] = $search;
            $where_values[] = $search;
            $where_values[] = $search;
        }
        
        $where_clause = implode( ' AND ', $where_clauses );
        $query = "SELECT COUNT(*) FROM {$wpdb->prefix}schedspot_bookings WHERE {$where_clause}";
        
        if ( ! empty( $where_values ) ) {
            $query = $wpdb->prepare( $query, $where_values );
        }
        
        return $wpdb->get_var( $query );
    }

    /**
     * Handle bulk actions.
     *
     * @since 1.0.0
     */
    private function handle_bulk_action() {
        if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'bulk-bookings' ) ) {
            wp_die( __( 'Security check failed.', 'schedspot' ) );
        }
        
        $action = sanitize_text_field( $_POST['action'] );
        $booking_ids = array_map( 'intval', $_POST['booking'] ?? array() );
        
        if ( empty( $booking_ids ) ) {
            return;
        }
        
        switch ( $action ) {
            case 'delete':
                $this->bulk_delete_bookings( $booking_ids );
                break;
            case 'confirm':
                $this->bulk_update_status( $booking_ids, 'confirmed' );
                break;
            case 'complete':
                $this->bulk_update_status( $booking_ids, 'completed' );
                break;
            case 'cancel':
                $this->bulk_update_status( $booking_ids, 'cancelled' );
                break;
        }
    }

    /**
     * Handle individual booking actions.
     *
     * @since 1.0.0
     */
    private function handle_individual_action() {
        $action = sanitize_text_field( $_GET['action'] );
        $booking_id = intval( $_GET['booking_id'] );
        
        if ( ! wp_verify_nonce( $_GET['_wpnonce'], 'booking_action_' . $booking_id ) ) {
            wp_die( __( 'Security check failed.', 'schedspot' ) );
        }
        
        switch ( $action ) {
            case 'view':
                $this->view_booking_details( $booking_id );
                break;
            case 'edit':
                $this->edit_booking( $booking_id );
                break;
            case 'delete':
                $this->delete_booking( $booking_id );
                break;
        }
    }

    /**
     * Bulk delete bookings.
     *
     * @since 1.0.0
     * @param array $booking_ids Booking IDs to delete.
     */
    private function bulk_delete_bookings( $booking_ids ) {
        global $wpdb;
        
        $placeholders = implode( ',', array_fill( 0, count( $booking_ids ), '%d' ) );
        $query = "DELETE FROM {$wpdb->prefix}schedspot_bookings WHERE id IN ($placeholders)";
        
        $deleted = $wpdb->query( $wpdb->prepare( $query, $booking_ids ) );
        
        if ( $deleted ) {
            $message = sprintf( 
                _n( '%d booking deleted.', '%d bookings deleted.', $deleted, 'schedspot' ), 
                $deleted 
            );
            add_settings_error( 'schedspot_bookings', 'bookings_deleted', $message, 'updated' );
        }
    }

    /**
     * Bulk update booking status.
     *
     * @since 1.0.0
     * @param array  $booking_ids Booking IDs to update.
     * @param string $status      New status.
     */
    private function bulk_update_status( $booking_ids, $status ) {
        global $wpdb;
        
        $placeholders = implode( ',', array_fill( 0, count( $booking_ids ), '%d' ) );
        $query = "UPDATE {$wpdb->prefix}schedspot_bookings SET status = %s WHERE id IN ($placeholders)";
        
        $params = array_merge( array( $status ), $booking_ids );
        $updated = $wpdb->query( $wpdb->prepare( $query, $params ) );
        
        if ( $updated ) {
            $message = sprintf( 
                _n( '%d booking updated.', '%d bookings updated.', $updated, 'schedspot' ), 
                $updated 
            );
            add_settings_error( 'schedspot_bookings', 'bookings_updated', $message, 'updated' );
        }
    }

    /**
     * View booking details.
     *
     * @since 1.0.0
     * @param int $booking_id Booking ID.
     */
    private function view_booking_details( $booking_id ) {
        $booking = SchedSpot_Booking::get_booking( $booking_id );
        
        if ( ! $booking ) {
            wp_die( __( 'Booking not found.', 'schedspot' ) );
        }
        
        include SCHEDSPOT_PLUGIN_DIR . 'templates/admin/booking-details.php';
    }

    /**
     * Edit booking.
     *
     * @since 1.0.0
     * @param int $booking_id Booking ID.
     */
    private function edit_booking( $booking_id ) {
        $booking = SchedSpot_Booking::get_booking( $booking_id );
        
        if ( ! $booking ) {
            wp_die( __( 'Booking not found.', 'schedspot' ) );
        }
        
        // Handle form submission
        if ( isset( $_POST['update_booking'] ) ) {
            $this->process_booking_update( $booking_id );
        }
        
        include SCHEDSPOT_PLUGIN_DIR . 'templates/admin/booking-edit.php';
    }

    /**
     * Delete booking.
     *
     * @since 1.0.0
     * @param int $booking_id Booking ID.
     */
    private function delete_booking( $booking_id ) {
        global $wpdb;
        
        $deleted = $wpdb->delete( 
            $wpdb->prefix . 'schedspot_bookings', 
            array( 'id' => $booking_id ), 
            array( '%d' ) 
        );
        
        if ( $deleted ) {
            wp_redirect( admin_url( 'admin.php?page=schedspot-bookings&deleted=1' ) );
            exit;
        } else {
            wp_die( __( 'Failed to delete booking.', 'schedspot' ) );
        }
    }

    /**
     * Process booking update.
     *
     * @since 1.0.0
     * @param int $booking_id Booking ID.
     */
    private function process_booking_update( $booking_id ) {
        if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'update_booking_' . $booking_id ) ) {
            wp_die( __( 'Security check failed.', 'schedspot' ) );
        }
        
        global $wpdb;
        
        $update_data = array(
            'status' => sanitize_text_field( $_POST['status'] ),
            'booking_date' => sanitize_text_field( $_POST['booking_date'] ),
            'booking_time' => sanitize_text_field( $_POST['booking_time'] ),
            'notes' => sanitize_textarea_field( $_POST['notes'] ),
        );
        
        $updated = $wpdb->update(
            $wpdb->prefix . 'schedspot_bookings',
            $update_data,
            array( 'id' => $booking_id ),
            array( '%s', '%s', '%s', '%s' ),
            array( '%d' )
        );
        
        if ( $updated !== false ) {
            wp_redirect( admin_url( 'admin.php?page=schedspot-bookings&updated=1' ) );
            exit;
        } else {
            add_settings_error( 'schedspot_bookings', 'update_failed', __( 'Failed to update booking.', 'schedspot' ), 'error' );
        }
    }

    /**
     * Handle AJAX status update.
     *
     * @since 1.0.0
     */
    public function handle_status_update() {
        if ( ! wp_verify_nonce( $_POST['nonce'], 'schedspot_booking_status' ) ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed.', 'schedspot' ) ) );
        }
        
        $booking_id = intval( $_POST['booking_id'] );
        $status = sanitize_text_field( $_POST['status'] );
        
        global $wpdb;
        $updated = $wpdb->update(
            $wpdb->prefix . 'schedspot_bookings',
            array( 'status' => $status ),
            array( 'id' => $booking_id ),
            array( '%s' ),
            array( '%d' )
        );
        
        if ( $updated !== false ) {
            wp_send_json_success( array( 
                'message' => __( 'Booking status updated successfully.', 'schedspot' ) 
            ) );
        } else {
            wp_send_json_error( array( 
                'message' => __( 'Failed to update booking status.', 'schedspot' ) 
            ) );
        }
    }

    /**
     * Handle booking deletion.
     *
     * @since 1.0.0
     */
    public function handle_booking_deletion() {
        if ( ! wp_verify_nonce( $_POST['nonce'], 'schedspot_delete_booking' ) ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed.', 'schedspot' ) ) );
        }
        
        $booking_id = intval( $_POST['booking_id'] );
        
        global $wpdb;
        $deleted = $wpdb->delete( 
            $wpdb->prefix . 'schedspot_bookings', 
            array( 'id' => $booking_id ), 
            array( '%d' ) 
        );
        
        if ( $deleted ) {
            wp_send_json_success( array( 
                'message' => __( 'Booking deleted successfully.', 'schedspot' ) 
            ) );
        } else {
            wp_send_json_error( array( 
                'message' => __( 'Failed to delete booking.', 'schedspot' ) 
            ) );
        }
    }

    /**
     * Handle refresh bookings AJAX.
     *
     * @since 1.0.0
     */
    public function handle_refresh_bookings() {
        if ( ! wp_verify_nonce( $_POST['nonce'], 'schedspot_refresh_bookings' ) ) {
            wp_send_json_error( array( 'message' => __( 'Security check failed.', 'schedspot' ) ) );
        }
        
        $bookings = $this->get_filtered_bookings();
        
        ob_start();
        foreach ( $bookings as $booking ) {
            include SCHEDSPOT_PLUGIN_DIR . 'templates/admin/booking-row.php';
        }
        $html = ob_get_clean();
        
        wp_send_json_success( array( 'html' => $html ) );
    }
}

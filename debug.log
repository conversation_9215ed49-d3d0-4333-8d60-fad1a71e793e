[23-Jun-2025 21:36:30 UTC] PHP Fatal error:  Uncaught Error: Call to private method SchedSpot_Booking::get_booking() from scope SchedSpot_Admin_Bookings in /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php:286
Stack trace:
#0 /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php(220): SchedSpot_Admin_Bookings->view_booking_details()
#1 /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php(60): SchedSpot_Admin_Bookings->handle_individual_action()
#2 /var/www/html/wordpress/wp-includes/class-wp-hook.php(324): SchedSpot_Admin_Bookings::bookings_page()
#3 /var/www/html/wordpress/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters()
#4 /var/www/html/wordpress/wp-includes/plugin.php(517): WP_Hook->do_action()
#5 /var/www/html/wordpress/wp-admin/admin.php(260): do_action()
#6 {main}
  thrown in /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php on line 286
[23-Jun-2025 21:36:35 UTC] PHP Fatal error:  Uncaught Error: Call to private method SchedSpot_Booking::get_booking() from scope SchedSpot_Admin_Bookings in /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php:286
Stack trace:
#0 /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php(220): SchedSpot_Admin_Bookings->view_booking_details()
#1 /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php(60): SchedSpot_Admin_Bookings->handle_individual_action()
#2 /var/www/html/wordpress/wp-includes/class-wp-hook.php(324): SchedSpot_Admin_Bookings::bookings_page()
#3 /var/www/html/wordpress/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters()
#4 /var/www/html/wordpress/wp-includes/plugin.php(517): WP_Hook->do_action()
#5 /var/www/html/wordpress/wp-admin/admin.php(260): do_action()
#6 {main}
  thrown in /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php on line 286
[23-Jun-2025 21:36:37 UTC] PHP Fatal error:  Uncaught Error: Call to private method SchedSpot_Booking::get_booking() from scope SchedSpot_Admin_Bookings in /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php:302
Stack trace:
#0 /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php(223): SchedSpot_Admin_Bookings->edit_booking()
#1 /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php(60): SchedSpot_Admin_Bookings->handle_individual_action()
#2 /var/www/html/wordpress/wp-includes/class-wp-hook.php(324): SchedSpot_Admin_Bookings::bookings_page()
#3 /var/www/html/wordpress/wp-includes/class-wp-hook.php(348): WP_Hook->apply_filters()
#4 /var/www/html/wordpress/wp-includes/plugin.php(517): WP_Hook->do_action()
#5 /var/www/html/wordpress/wp-admin/admin.php(260): do_action()
#6 {main}
  thrown in /var/www/html/wordpress/wp-content/plugins/schedspot/admin/class-schedspot-admin-bookings.php on line 302
[23-Jun-2025 21:37:18 UTC] WordPress database error Unknown column 'price' in 'SELECT' for query SELECT id, name, price FROM wp_schedspot_services WHERE status = 'active' ORDER BY name ASC made by do_action('schedspot_page_schedspot-workers'), WP_Hook->do_action, WP_Hook->apply_filters, SchedSpot_Admin_Workers::workers_page, SchedSpot_Admin_Workers->get_services
[23-Jun-2025 21:39:40 UTC] WordPress database error Unknown column 'booking_time' in 'ORDER BY' for query SELECT * FROM wp_schedspot_bookings 
             WHERE worker_id = 1 
             ORDER BY booking_date DESC, booking_time DESC 
             LIMIT 10 made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data
[23-Jun-2025 21:39:40 UTC] WordPress database error Unknown column 'total_price' in 'SELECT' for query SELECT SUM(total_price) FROM wp_schedspot_bookings 
                 WHERE worker_id = 1 AND status IN ('confirmed', 'completed') made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data
[23-Jun-2025 21:39:40 UTC] WordPress database error Unknown column 'total_price' in 'SELECT' for query SELECT SUM(total_price) FROM wp_schedspot_bookings 
                 WHERE worker_id = 1 AND status IN ('confirmed', 'completed') 
                 AND MONTH(booking_date) = MONTH(CURDATE()) AND YEAR(booking_date) = YEAR(CURDATE()) made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data
[23-Jun-2025 21:39:40 UTC] WordPress database error Unknown column 'booking_time' in 'ORDER BY' for query SELECT * FROM wp_schedspot_bookings 
             WHERE worker_id = 1 
             AND booking_date >= CURDATE() 
             AND status IN ('pending', 'confirmed')
             ORDER BY booking_date ASC, booking_time ASC 
             LIMIT 5 made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data, SchedSpot_Shortcode_Dashboard->get_upcoming_bookings
[23-Jun-2025 21:39:42 UTC] WordPress database error Unknown column 'booking_time' in 'ORDER BY' for query SELECT * FROM wp_schedspot_bookings 
             WHERE worker_id = 1 
             ORDER BY booking_date DESC, booking_time DESC 
             LIMIT 10 made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data
[23-Jun-2025 21:39:42 UTC] WordPress database error Unknown column 'total_price' in 'SELECT' for query SELECT SUM(total_price) FROM wp_schedspot_bookings 
                 WHERE worker_id = 1 AND status IN ('confirmed', 'completed') made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data
[23-Jun-2025 21:39:42 UTC] WordPress database error Unknown column 'total_price' in 'SELECT' for query SELECT SUM(total_price) FROM wp_schedspot_bookings 
                 WHERE worker_id = 1 AND status IN ('confirmed', 'completed') 
                 AND MONTH(booking_date) = MONTH(CURDATE()) AND YEAR(booking_date) = YEAR(CURDATE()) made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data
[23-Jun-2025 21:39:42 UTC] WordPress database error Unknown column 'booking_time' in 'ORDER BY' for query SELECT * FROM wp_schedspot_bookings 
             WHERE worker_id = 1 
             AND booking_date >= CURDATE() 
             AND status IN ('pending', 'confirmed')
             ORDER BY booking_date ASC, booking_time ASC 
             LIMIT 5 made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data, SchedSpot_Shortcode_Dashboard->get_upcoming_bookings
[23-Jun-2025 21:39:59 UTC] WordPress database error Unknown column 'booking_time' in 'ORDER BY' for query SELECT * FROM wp_schedspot_bookings 
             WHERE worker_id = 1 
             ORDER BY booking_date DESC, booking_time DESC 
             LIMIT 10 made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data
[23-Jun-2025 21:39:59 UTC] WordPress database error Unknown column 'total_price' in 'SELECT' for query SELECT SUM(total_price) FROM wp_schedspot_bookings 
                 WHERE worker_id = 1 AND status IN ('confirmed', 'completed') made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data
[23-Jun-2025 21:39:59 UTC] WordPress database error Unknown column 'total_price' in 'SELECT' for query SELECT SUM(total_price) FROM wp_schedspot_bookings 
                 WHERE worker_id = 1 AND status IN ('confirmed', 'completed') 
                 AND MONTH(booking_date) = MONTH(CURDATE()) AND YEAR(booking_date) = YEAR(CURDATE()) made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data
[23-Jun-2025 21:39:59 UTC] WordPress database error Unknown column 'booking_time' in 'ORDER BY' for query SELECT * FROM wp_schedspot_bookings 
             WHERE worker_id = 1 
             AND booking_date >= CURDATE() 
             AND status IN ('pending', 'confirmed')
             ORDER BY booking_date ASC, booking_time ASC 
             LIMIT 5 made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Dashboard::render, SchedSpot_Shortcode_Dashboard->render_dashboard, SchedSpot_Shortcode_Dashboard->get_dashboard_data, SchedSpot_Shortcode_Dashboard->get_worker_dashboard_data, SchedSpot_Shortcode_Dashboard->get_upcoming_bookings
[23-Jun-2025 21:40:16 UTC] WordPress database error Unknown column 'status' in 'WHERE' for query SELECT * FROM wp_schedspot_services WHERE status = 'active' ORDER BY name ASC made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Booking_Form::render, SchedSpot_Shortcode_Booking_Form->render_form, SchedSpot_Shortcode_Booking_Form->get_available_services
[23-Jun-2025 21:40:25 UTC] WordPress database error Unknown column 'recipient_id' in 'SELECT' for query SELECT DISTINCT 
                CASE 
                    WHEN sender_id = 1 THEN recipient_id 
                    ELSE sender_id 
                END as partner_id
             FROM wp_schedspot_messages 
             WHERE sender_id = 1 OR recipient_id = 1 made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Messages::render, SchedSpot_Shortcode_Messages->render_messages, SchedSpot_Shortcode_Messages->get_user_conversations
[23-Jun-2025 21:40:37 UTC] PHP Warning:  Undefined array key "postId" in /var/www/html/wordpress/wp-includes/blocks/comments.php on line 31
[23-Jun-2025 21:40:37 UTC] PHP Warning:  Attempt to read property "post_type" on null in /var/www/html/wordpress/wp-includes/link-template.php on line 4179
[23-Jun-2025 21:40:37 UTC] PHP Warning:  Attempt to read property "post_type" on null in /var/www/html/wordpress/wp-includes/link-template.php on line 4181
[23-Jun-2025 21:40:39 UTC] WordPress database error Unknown column 'recipient_id' in 'SELECT' for query SELECT DISTINCT 
                CASE 
                    WHEN sender_id = 1 THEN recipient_id 
                    ELSE sender_id 
                END as partner_id
             FROM wp_schedspot_messages 
             WHERE sender_id = 1 OR recipient_id = 1 made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Messages::render, SchedSpot_Shortcode_Messages->render_messages, SchedSpot_Shortcode_Messages->get_user_conversations
[23-Jun-2025 21:40:41 UTC] PHP Warning:  Undefined array key "postId" in /var/www/html/wordpress/wp-includes/blocks/comments.php on line 31
[23-Jun-2025 21:40:41 UTC] PHP Warning:  Attempt to read property "post_type" on null in /var/www/html/wordpress/wp-includes/link-template.php on line 4179
[23-Jun-2025 21:40:41 UTC] PHP Warning:  Attempt to read property "post_type" on null in /var/www/html/wordpress/wp-includes/link-template.php on line 4181
[23-Jun-2025 21:40:43 UTC] WordPress database error Unknown column 'recipient_id' in 'SELECT' for query SELECT DISTINCT 
                CASE 
                    WHEN sender_id = 1 THEN recipient_id 
                    ELSE sender_id 
                END as partner_id
             FROM wp_schedspot_messages 
             WHERE sender_id = 1 OR recipient_id = 1 made by require('wp-blog-header.php'), require_once('wp-includes/template-loader.php'), include('wp-includes/template-canvas.php'), get_the_block_template_html, do_blocks, render_block, WP_Block->render, WP_Block->render, WP_Block->render, render_block_core_post_content, apply_filters('the_content'), WP_Hook->apply_filters, do_shortcode, preg_replace_callback, do_shortcode_tag, SchedSpot_Shortcode_Messages::render, SchedSpot_Shortcode_Messages->render_messages, SchedSpot_Shortcode_Messages->get_user_conversations
